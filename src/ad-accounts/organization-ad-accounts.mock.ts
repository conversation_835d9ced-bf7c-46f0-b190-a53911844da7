import { AccountType } from '../workspaces/entities/account-type.entity';
import { Organization } from '../organizations/entities/organization.entity';
import { Workspace } from '../workspaces/entities/workspace.entity';
import { PlatformAdAccountToWorkspace } from './entities/ad-account-workspace-map.entity';
import { PlatformAdAccount } from './entities/ad-account.entity';
import { User } from '../entities/user.entity';
import { ImportState } from '../common/constants/constants';

const currentDate: Date = new Date(Date.now());

export const mockOrganization: Organization = {
  id: '1',
  name: 'Test Organization',
  status: 'active',
  workspaces: [],
  dateCreated: currentDate,
  lastUpdated: currentDate,
  isPersonal: false,
};

export const mockWorkspace1: Workspace = {
  id: 1,
  name: 'test_workspace_1',
  logoUrl: 'https://s3.amazonaws.com/brandcrumb-dev/brandcrumb-logo.png',
  accountType: new AccountType(),
  organization: mockOrganization,
  organizationId: '1',
  accountTypeId: 1,
  isPrimary: true,
  version: 1,
  includeDraftDownloadUrl: true,
  projectFinalAssetsRequired: true,
  draftReview: 'none',
  parentPartnerId: 1,
  makerId: 1,
  pricingRatioTierId: 1,
  dateCreated: currentDate,
  lastUpdated: currentDate,
  personal: true,
  workspaceUsers: [],
  workspaceManagers: [],
  markets: [],
  workspaceMarkets: [],
  brands: [],
  workspaceBrands: [],
  users: [],
  platformAdAccountToWorkspaces: [],
  findMyTeamEnabled: false,
  featureWorkspaces: [],
};

export const mockAdAccount2: PlatformAdAccount = {
  id: 2,
  platformAccountName: 'test_ad_account_1',
  platform: 'facebook',
  platformAccountId: '1',
  platformUserId: '123',
  organizationPlatformAdAccountMap: [],
  PlatformAdAccountToWorkspace: [],
  organizationAdAccountUserPermissions: [],
  canAccess: 1,
  userId: 1,
  user: new User(),
  lastUpdated: currentDate,
  dateCreated: currentDate,
  lastImportStartDate: currentDate,
  lastImportCompleteDate: currentDate,
  lastImportSuccessDate: currentDate,
  lastImportStatus: ImportState.SUCCESS,
  platformOrganizationId: '1',
  processingCompleted: 1,
  processingCompletedDate: currentDate,
  importV3Enabled: 1,
  currencyCode: 'USD',
  accountImportStatus: null,
  importActive: 1,
  importStatuses: [],
  platformAdAccountBrandMap: [],
  platformAdAccountMarketMap: [],
  workspaceAdAccountMap: [],
  platformAdAccountSequentialFailures: [],
};

export const mockWorkspaceAdAccountMap: PlatformAdAccountToWorkspace = {
  partnerId: 1,
  platformAdAccountId: 1,
  workspace: mockWorkspace1,
  platformAdAccount: mockAdAccount2,
};

mockWorkspace1.platformAdAccountToWorkspaces.push(mockWorkspaceAdAccountMap);
mockOrganization.workspaces.push(mockWorkspace1);
