import {
  <PERSON>NotEmpty,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>String,
  <PERSON>idateNested,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateWorkspaceDto } from '../../workspaces/dto/create-workspace.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOrganizationDto {
  /**
   * Name of the Organization
   * @example "VidMob"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Status of the Organization
   * @example "ENABLED"
   */
  @AutoMap()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  status?: string;

  /**
   * ACS session timeout in minutes
   * @example 60
   */
  @AutoMap()
  @ApiProperty({
    required: false,
    description: 'ACS session timeout in minutes',
    default: 60,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  sessionTimeoutMin?: number;

  /**
   * List of workspaces connected to the Organization
   */
  @AutoMap(() => CreateWorkspaceDto)
  @ApiProperty({
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => CreateWorkspaceDto)
  workspaces?: CreateWorkspaceDto[];
}
