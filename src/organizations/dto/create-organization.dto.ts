import {
  IsNotEmpty,
  <PERSON><PERSON><PERSON>al,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateWorkspaceDto } from '../../workspaces/dto/create-workspace.dto';
import { AutoMap } from '@automapper/classes';
import { ApiProperty } from '@nestjs/swagger';

export class CreateOrganizationDto {
  /**
   * Name of the Organization
   * @example "VidMob"
   */
  @AutoMap()
  @IsNotEmpty()
  @IsString()
  name: string;

  /**
   * Status of the Organization
   * @example "ENABLED"
   */
  @AutoMap()
  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsNotEmpty()
  @IsString()
  status?: string;

  /**
   * List of workspaces connected to the Organization
   */
  @AutoMap(() => CreateWorkspaceDto)
  @ApiProperty({
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => CreateWorkspaceDto)
  workspaces?: CreateWorkspaceDto[];
}
