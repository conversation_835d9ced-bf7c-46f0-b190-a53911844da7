/**
 * Data Transfer Object (DTO) representing the organization entity
 */
import { AutoMap } from '@automapper/classes';
import { ReadWorkspaceDto } from '../../workspaces/dto/read-workspace.dto';

export class ReadOrganizationDto {
  /**
   * System assigned Id of the Organization
   * @example 123e4567-e89b-12d3-a456-426614174000
   */
  @AutoMap()
  id: string;

  /**
   * Name of the Organization
   * @example "Acme Inc"
   */
  @AutoMap()
  name: string;

  /**
   * Status of the Organization
   * @example "ENABLED"
   */
  @AutoMap()
  status: string;

  /**
   * Creation date of the Org. This is of the format YYYY-MM-DD
   */
  @AutoMap()
  dateCreated: Date;

  /**
   * Updated date of the Org
   */
  @AutoMap()
  lastUpdated: Date;

  @AutoMap()
  spendEnabled?: boolean;

  /**
   * List of workspaces connected to the Organization
   */
  @AutoMap(() => ReadWorkspaceDto)
  workspaces: ReadWorkspaceDto[];
}
