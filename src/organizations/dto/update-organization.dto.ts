import { CreateOrganizationDto } from './create-organization.dto';
import { ApiExtraModels, PartialType } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { AutoMap } from '@automapper/classes';

@ApiExtraModels(CreateOrganizationDto)
export class UpdateOrganizationDto extends PartialType(CreateOrganizationDto) {
  /**
   * Status of the Organization
   */
  @AutoMap()
  @IsOptional()
  @IsString()
  status: string;
}
