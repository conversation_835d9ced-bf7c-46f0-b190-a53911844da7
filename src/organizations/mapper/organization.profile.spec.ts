import { Test, TestingModule } from '@nestjs/testing';
import { AutomapperModule, getMapperToken } from '@automapper/nestjs';
import { Mapper } from '@automapper/core';
import { OrganizationProfile } from './organization.profile';
import { CreateOrganizationDto } from '../dto/create-organization.dto';
import { Organization } from '../entities/organization.entity';
import { classes } from '@automapper/classes';
import { UpdateOrganizationDto } from '../dto/update-organization.dto';
import { ReadOrganizationDto } from '../dto/read-organization.dto';
import { DeleteOrganizationDto } from '../dto/delete-organization.dto';

describe('OrganizationProfile', () => {
  let mapper: Mapper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [AutomapperModule.forRoot({ strategyInitializer: classes() })],
      providers: [OrganizationProfile],
    }).compile();

    const organizationProfile: OrganizationProfile =
      module.get<OrganizationProfile>(OrganizationProfile);

    mapper = module.get<Mapper>(getMapperToken());
    organizationProfile.profile(mapper);
  });

  it('should map CreateOrganizationDto to Organization', () => {
    const createOrganizationDto: CreateOrganizationDto = {
      name: 'VidMob',
      status: 'ENABLED',
    };

    const organization = mapper.map(
      createOrganizationDto,
      CreateOrganizationDto,
      Organization,
    );
    expect(organization).toBeDefined();
    expect(organization).toBeDefined();
    expect(organization.name).toBe('VidMob');
    expect(organization.status).toBe('ENABLED');
  });

  it('should map UpdateOrganizationDto to Organization', () => {
    const updateOrganizationDto: UpdateOrganizationDto = {
      status: 'UPDATED_STATUS',
    };

    const organization = mapper.map(
      updateOrganizationDto,
      UpdateOrganizationDto,
      Organization,
    );
    expect(organization).toBeDefined();
    expect(organization.status).toBe('UPDATED_STATUS');
  });

  it('should map Organization to ReadOrganizationDto', () => {
    const organization: Organization = {
      id: '123456',
      name: 'Example Organization',
      status: 'ACTIVE',
      dateCreated: new Date('2023-05-01'),
      lastUpdated: new Date('2023-05-10'),
      workspaces: [],
      isPersonal: false,
    };

    const readOrganizationDto = mapper.map(
      organization,
      Organization,
      ReadOrganizationDto,
    );
    expect(readOrganizationDto).toBeDefined();
    expect(readOrganizationDto.id).toBe('123456');
    expect(readOrganizationDto.name).toBe('Example Organization');
    expect(readOrganizationDto.status).toBe('ACTIVE');
    expect(readOrganizationDto.dateCreated).toEqual(new Date('2023-05-01'));
    expect(readOrganizationDto.lastUpdated).toEqual(new Date('2023-05-10'));
  });

  it('should map Organization to DeleteOrganizationDto', () => {
    const organization: Organization = {
      id: '123456',
      name: 'Example Organization',
      status: 'ACTIVE',
      dateCreated: new Date('2023-05-01'),
      lastUpdated: new Date('2023-05-10'),
      workspaces: [
        // Provide any necessary properties for the workspaces
      ],
      isPersonal: false,
    };

    const deleteOrganizationDto = mapper.map(
      organization,
      Organization,
      DeleteOrganizationDto,
    );
    expect(deleteOrganizationDto).toBeDefined();
    expect(deleteOrganizationDto.id).toBe('123456');
  });
});
