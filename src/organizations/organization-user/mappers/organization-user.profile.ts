import { Mapper, createMap, forM<PERSON>ber, mapFrom } from '@automapper/core';
import { AutomapperProfile, InjectMapper } from '@automapper/nestjs';
import { Injectable } from '@nestjs/common';
import { OrganizationUserResponseDto } from '../dto/organization-user-response.dto';
import { OrganizationUserRoleResponseDto } from '../dto/organization-user-role-response.dto';
import { User } from '../../../entities/user.entity';
import { Role } from '../../../entities/role.entity';
import { ReadOrganizationUserWorkspaceDto } from '../dto/read-organization-user-workspace.dto';
import { ReadOrganizationUserWorkspaceEntity } from '../dto/read-organization-user-workspace-entity.dto';
import { ReadLightWeightUserDto } from '../dto/read-light-weight-user.dto';
import { ReadOrganizationUserDataDto } from '../dto/read-organization-user-data.dto';
import { ReadWorkspaceDto } from '../dto/read-workspace.dto';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { OrganizationUserRole } from '../entities/organization-user-role.entity';

@Injectable()
export class OrganizationUserProfile extends AutomapperProfile {
  constructor(@InjectMapper() mapper: Mapper) {
    super(mapper);
  }

  override get profile() {
    return (mapper: Mapper) => {
      createMap(mapper, User, OrganizationUserResponseDto);

      createMap(mapper, Role, OrganizationUserRoleResponseDto);

      createMap(
        mapper,
        ReadOrganizationUserWorkspaceEntity,
        ReadOrganizationUserWorkspaceDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.organization_id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.organization_name),
        ),
        forMember(
          (dest) => dest.associatedWorkspaces,
          mapFrom((src) => parseInt(src.associatedWorkspaces)),
        ),
        forMember(
          (dest) => dest.redirectWorkspaceId,
          mapFrom((src) => parseInt(src.redirectWorkspaceId)),
        ),
        forMember(
          (dest) => dest.importsEnabled,
          mapFrom((src) => (src.imports_enabled === '1' ? true : false)),
        ),
      );

      createMap(
        mapper,
        User,
        ReadLightWeightUserDto,
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.displayName),
        ),
      );

      createMap(mapper, User, ReadOrganizationUserDataDto);

      createMap(mapper, Workspace, ReadWorkspaceDto);

      createMap(
        mapper,
        OrganizationUserRole,
        OrganizationUserRoleResponseDto,
        forMember(
          (dest) => dest.id,
          mapFrom((src) => src.role.id),
        ),
        forMember(
          (dest) => dest.name,
          mapFrom((src) => src.role.name),
        ),
        forMember(
          (dest) => dest.type,
          mapFrom((src) => src.role.type),
        ),
        forMember(
          (dest) => dest.identifier,
          mapFrom((src) => src.role.identifier),
        ),
        forMember(
          (dest) => dest.description,
          mapFrom((src) => src.role.description),
        ),
      );
    };
  }
}
