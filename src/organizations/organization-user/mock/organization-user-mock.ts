import { User } from 'src/entities/user.entity';
import { WorkspaceManager } from 'src/workspaces/entities/workspace-manager.entity';
import { WorkspaceUser } from 'src/workspaces/entities/workspace-user.entity';
import { Workspace } from 'src/workspaces/entities/workspace.entity';
import { UserOrganizationMap } from '../entities/user-organization-map.entity';
import { OrganizationUserRole } from '../entities/organization-user-role.entity';
import { Role } from 'src/entities/role.entity';
import { LoginEvent } from 'src/login-event/entities/login-event.entity';

export const organization = {
  id: 'xxx',
  name: 'test org',
  status: 'enabled',
  dateCreated: new Date(Date.now()),
  lastUpdated: new Date(Date.now()),
  workspaces: [],
  isPersonal: false,
  sessionTimeoutMin: 60,
};

export const user1 = {
  id: 1,
  firstName: 'test',
  username: 'test-user-1',
  lastName: 'user 1',
  photo: null,
  displayName: 'test user 1',
  email: '<EMAIL>',
  jobTitle: null,
} as User;

export const user2 = {
  id: 2,
  firstName: 'test',
  username: 'test-user-2',
  lastName: 'user 2',
  photo: null,
  displayName: 'test user 2',
  email: '<EMAIL>',
  jobTitle: null,
} as User;

export const user3 = {
  id: 3,
  firstName: 'test',
  username: 'test-user-3',
  lastName: 'user 3',
  photo: null,
  displayName: 'test user 3',
  email: '<EMAIL>',
  jobTitle: null,
} as User;

export const workspace1 = {
  id: 1,
  name: 'workspace 1',
  isPrimary: false,
  personal: false,
} as Workspace;

export const workspace2 = {
  id: 2,
  name: 'workspace 2',
  isPrimary: false,
  personal: false,
} as Workspace;

export const workspace3 = {
  id: 3,
  name: 'workspace 3',
  isPrimary: false,
  personal: false,
} as Workspace;

export const workspace4 = {
  id: 4,
  name: 'workspace 4',
  isPrimary: false,
  personal: false,
} as Workspace;

export const adminRole: Role = {
  id: 1,
  name: 'Admin',
  type: 'organization_entity',
  identifier: 'ORG_ADMIN',
  description: '',
};

export const standardRole: Role = {
  id: 2,
  name: 'Standard',
  type: 'organization_entity',
  identifier: 'ORG_STANDARD',
  description: '',
};

export const userOrganizationMaps: UserOrganizationMap[] = [
  {
    organizationId: organization.id,
    userId: user1.id,
    organization,
    user: user1,
  },
  {
    organizationId: organization.id,
    userId: user2.id,
    organization,
    user: user2,
  },
  {
    organizationId: organization.id,
    userId: user3.id,
    organization,
    user: user3,
  },
];

export const organizationUserRoles: OrganizationUserRole[] = [
  {
    organizationId: organization.id,
    userId: user1.id,
    roleId: adminRole.id,
    organization,
    user: user1,
    role: adminRole,
    dateCreated: new Date(Date.now()),
  },
  {
    organizationId: organization.id,
    userId: user2.id,
    roleId: standardRole.id,
    organization,
    user: user2,
    role: standardRole,
    dateCreated: new Date(Date.now()),
  },
  {
    organizationId: organization.id,
    userId: user3.id,
    roleId: standardRole.id,
    organization,
    user: user3,
    role: standardRole,
    dateCreated: new Date(Date.now()),
  },
];

export const workspaceUsers: WorkspaceUser[] = [
  {
    userId: user1.id,
    workspaceId: workspace1.id,
    workspace: workspace1,
  } as WorkspaceUser,
  {
    userId: user2.id,
    workspaceId: workspace1.id,
    workspace: workspace1,
  } as WorkspaceUser,
  {
    userId: user1.id,
    workspaceId: workspace2.id,
    workspace: workspace2,
  } as WorkspaceUser,
];

export const workspaceManagers: WorkspaceManager[] = [
  {
    managerId: user1.id,
    workspaceId: workspace1.id,
    workspace: workspace1,
  } as WorkspaceManager,
  {
    managerId: user1.id,
    workspaceId: workspace3.id,
    workspace: workspace3,
  } as WorkspaceManager,
  {
    managerId: user2.id,
    workspaceId: workspace4.id,
    workspace: workspace4,
  } as WorkspaceManager,
];

export const user1LoginEvent = {
  userId: user1.id,
  dateCreated: new Date(Date.now()),
} as LoginEvent;

export const user2LoginEvent = {
  userId: user2.id,
  dateCreated: new Date(Date.now()),
} as LoginEvent;

export const loginEvents: LoginEvent[] = [user1LoginEvent, user2LoginEvent];
