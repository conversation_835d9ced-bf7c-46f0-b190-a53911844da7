import { Test, TestingModule } from '@nestjs/testing';
import { OrganizationsService } from './organizations.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Organization } from './entities/organization.entity';
import { Repository } from 'typeorm';
import { getMapperToken } from '@automapper/nestjs';
import { Mapper, createMapper } from '@automapper/core';
import { classes } from '@automapper/classes';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { DISABLED, ENABLED } from 'src/common/constants/constants';
import { OrganizationProfile } from './mapper/organization.profile';
import { ReadOrganizationDto } from './dto/read-organization.dto';
import { OrganizationFeatureWhitelist } from './entities/organization-feature-whitelist.entity';

describe('OrganizationsService', () => {
  let organizationsService: OrganizationsService;
  let organizationRepository: Repository<Organization>;
  let classMapper: Mapper;

  const testDate = new Date();
  const mockOrganization = new Organization();
  mockOrganization.id = 'generated-id';
  mockOrganization.name = 'Test Organization';
  mockOrganization.status = ENABLED;
  mockOrganization.dateCreated = testDate;
  mockOrganization.lastUpdated = testDate;
  mockOrganization.sessionTimeoutMin = 60;

  const mockOrganizationDto = new CreateOrganizationDto();
  mockOrganizationDto.name = mockOrganization.name;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OrganizationsService,
        OrganizationProfile,
        {
          provide: getRepositoryToken(Organization),
          useClass: Repository,
        },
        {
          provide: getRepositoryToken(OrganizationFeatureWhitelist),
          useClass: Repository,
        },
        {
          provide: getMapperToken(),
          useValue: createMapper({
            strategyInitializer: classes(),
          }),
        },
      ],
    }).compile();

    classMapper = module.get<Mapper>(getMapperToken());

    organizationsService =
      module.get<OrganizationsService>(OrganizationsService);
    organizationRepository = module.get<Repository<Organization>>(
      getRepositoryToken(Organization),
    );
  });

  it('should be defined', () => {
    expect(organizationsService).toBeDefined();
  });

  it('should create an organization', async () => {
    jest
      .spyOn(organizationRepository, 'save')
      .mockImplementation((createOrganizationDto: any) => {
        return {
          ...createOrganizationDto,
          id: 'generated-id',
          dateCreated: testDate,
          lastUpdated: testDate,
          sessionTimeoutMin: 60,
        };
      });

    const result = await organizationsService.create(mockOrganizationDto);

    expect(result).toEqual({
      ...mockOrganization,
      spendEnabled: false,
      workspaces: undefined,
    });
    expect(result).toBeInstanceOf(ReadOrganizationDto);
  });
});
