export enum ORGANIZATION_USER_FILTER_BY {
  VIDMOB_ONLY = 'VIDMOB_ONLY',
  NO_VIDMOB = 'NO_VIDMOB',
}

export const featureBrandGovernanceIdentifier = 'BRAND-GOVERNANCE';

export const featureBringYourOwnCreatorIdentifier = 'BRING-YOUR-OWN-CREATOR';

export const SELF_MANAGED_IDENTIFIER = 'Self Managed';
export const ACCOUNT_TYPE_PUBLIC_SCOPE = 'PUBLIC';

export const FEATURE_INTEGRATION_ACCOUNT_IMPORTS_ENABLED =
  'INTEGRATION-ACCOUNT-IMPORTS-ENABLED';

export const FEATURE_INTEGRATION_IMPORT_SPEND_ENABLED = 'IMPORT-SPEND-ENABLED';

export enum FEATURE_TYPE {
  ACCOUNT_TYPE = 'ACCOUNT_TYPE',
  PERSON = 'PERSON',
  ORGANIZATION = 'ORGANIZATION',
}
